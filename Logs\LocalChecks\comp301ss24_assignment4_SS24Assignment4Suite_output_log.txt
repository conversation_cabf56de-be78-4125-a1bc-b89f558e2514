
0,Wed Jul 09 20:04:16 EDT 2025*PRE_OUTPUT*
>>Running suite A4Style
<<
>>Running test A4PackageDeclarations
<<
>>Running test AssertingBridgeSceneDynamics
<<
>>Running test A4SimplifyBooleanExpressions
<<
>>Running test A4SimplifyBooleanReturns
<<
>>Running test A4NoHiddenFields
<<
>>Running test A4NamingConventions
<<
>>Running test A4InterfaceAsType
<<
>>Running test A4NamedConstants
<<
>>Running test A4NoStarImports
<<
>>Running test A4PublicMethodsOverride
<<
>>Running test A4MnemonicNames
<<
>>Running test A4Encapsulation
<<
>>Running test A4NonPublicAccessModifiersMatched
<<
>>Running test A4CommonPropertiesAreInherited
<<
>>Running test A4CommonSignaturesAreInherited
<<
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
AssertingBridgeSceneDynamics,0.0% complete,0.0,50.0,No compiled classes found in bin, see transcript for compile errors
<<
>>Test Result:
A4PackageDeclarations,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4SimplifyBooleanExpressions,0.0% complete,0.0,1.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4SimplifyBooleanReturns,0.0% complete,0.0,1.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NoHiddenFields,0.0% complete,0.0,1.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NamingConventions,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4InterfaceAsType,0.0% complete,0.0,7.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NamedConstants,0.0% complete,0.0,3.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NoStarImports,0.0% complete,0.0,1.0,No checkstyle output, check console error messages
<<
>>Test Result:
A4PublicMethodsOverride,0.0% complete,0.0,5.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4MnemonicNames,0.0% complete,0.0,5.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4Encapsulation,0.0% complete,0.0,3.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4NonPublicAccessModifiersMatched,0.0% complete,0.0,5.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4CommonPropertiesAreInherited,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
>>Test Result:
A4CommonSignaturesAreInherited,0.0% complete,0.0,2.0,
Preceding test AssertingBridgeSceneDynamics failed.
Please correct the problems identified by preceding test:AssertingBridgeSceneDynamics before running this test
<<
*END_OUTPUT*


